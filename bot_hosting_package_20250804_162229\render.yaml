services:
  - type: web
    name: minecraft-mods-bot
    env: python
    plan: free
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
      python render_network_fix.py
    startCommand: python start_render.py
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: RENDER
        value: true
      - key: RENDER_SERVICE_TYPE
        value: web
      - key: PYTHONUNBUFFERED
        value: 1
      - key: PYTHONIOENCODING
        value: utf-8
      - key: BOT_TOKEN
        sync: false
      - key: ADMIN_CHAT_ID
        sync: false
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_KEY
        sync: false
      - key: WEB_SERVER_URL
        value: https://1c547fe5.sendaddons.pages.dev
      - key: WEB_SERVER_PORT
        value: 5000
      - key: TELEGRAM_WEB_APP_PORT
        value: 5001
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      - key: LOG_LEVEL
        value: INFO
