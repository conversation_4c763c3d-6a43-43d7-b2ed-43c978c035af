#!/usr/bin/env python3
"""
ملف بدء التشغيل المخصص لـ Render
يشغل البوت مع خادم ويب للمراقبة
"""

import os
import sys
import logging
import threading
import time
from pathlib import Path

# إعداد المسارات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد السجلات المبكر
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.StreamHandler(sys.stderr)
    ]
)

logger = logging.getLogger(__name__)

def setup_environment():
    """إعداد البيئة للعمل على Render"""
    logger.info("🚀 بدء إعداد البيئة لـ Render...")
    
    # إنشاء المجلدات المطلوبة
    required_dirs = [
        'logs',
        'results', 
        'assets/backgrounds/video',
        'assets/backgrounds/audio',
        'assets/temp'
    ]
    
    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ تم إنشاء المجلد: {dir_path}")
    
    # تعيين متغيرات البيئة الافتراضية
    default_env_vars = {
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        'TZ': 'UTC'
    }
    
    for key, value in default_env_vars.items():
        if key not in os.environ:
            os.environ[key] = value
            logger.info(f"✅ تم تعيين متغير البيئة: {key}={value}")
    
    logger.info("✅ تم إعداد البيئة بنجاح")

def check_configuration():
    """فحص الإعدادات المطلوبة"""
    logger.info("🔍 فحص الإعدادات...")

    try:
        # استيراد وتشغيل إعداد الإعدادات
        from config_render import setup_render_environment
        config_ok = setup_render_environment()

        if config_ok:
            logger.info("✅ جميع الإعدادات صحيحة")
            return True
        else:
            logger.warning("⚠️ بعض الإعدادات مفقودة، سيتم المتابعة مع الإعدادات الافتراضية")
            return False

    except Exception as e:
        logger.error(f"❌ خطأ في إعداد الإعدادات: {e}")
        return False

def run_bot():
    """تشغيل البوت الرئيسي"""
    logger.info("🤖 بدء تشغيل البوت...")
    
    try:
        # استيراد وتشغيل البوت الرئيسي
        import main
        logger.info("✅ تم تشغيل البوت بنجاح")
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        raise

def run_web_server():
    """تشغيل خادم الويب للمراقبة"""
    logger.info("🌐 بدء تشغيل خادم الويب...")
    
    try:
        from web_server import run_web_server
        run_web_server()
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل خادم الويب: {e}")
        # لا نوقف البوت إذا فشل خادم الويب
        pass

def main():
    """الدالة الرئيسية"""
    logger.info("=" * 50)
    logger.info("🚀 Reddit Video Maker Bot - Render Deployment")
    logger.info("=" * 50)
    
    try:
        # إعداد البيئة
        setup_environment()
        
        # فحص الإعدادات
        config_ok = check_configuration()
        if not config_ok:
            logger.warning("⚠️ بعض الإعدادات مفقودة، سيتم المتابعة مع الإعدادات الافتراضية")
        
        # تحديد نوع الخدمة
        service_type = os.getenv('RENDER_SERVICE_TYPE', 'worker')
        logger.info(f"📋 نوع الخدمة: {service_type}")
        
        if service_type == 'web':
            # إذا كانت خدمة ويب، نشغل خادم الويب مع البوت
            logger.info("🌐 تشغيل كخدمة ويب...")
            
            # تشغيل البوت في خيط منفصل
            bot_thread = threading.Thread(target=run_bot, daemon=True)
            bot_thread.start()
            
            # تشغيل خادم الويب في الخيط الرئيسي
            run_web_server()
            
        else:
            # إذا كانت خدمة عامل، نشغل البوت فقط
            logger.info("⚙️ تشغيل كخدمة عامل...")
            run_bot()
            
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
        
    except Exception as e:
        logger.error(f"❌ خطأ حرج: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
