#!/usr/bin/env python
import math
import sys
import logging
import os
from os import name
from pathlib import Path
from subprocess import Popen
from typing import NoReturn

from prawcore import ResponseException

from reddit.subreddit import get_subreddit_threads
from utils import settings
from utils.cleanup import cleanup
from utils.console import print_markdown, print_step, print_substep
from utils.ffmpeg_install import ffmpeg_install
from utils.id import id
from utils.version import checkversion
from utils.smart_config_checker import check_config_with_telegram_notifications
from video_creation.background import (
    chop_background,
    download_background_audio,
    download_background_video,
    get_background_config,
)
from video_creation.final_video import make_final_video
from video_creation.screenshot_downloader import get_screenshots_of_reddit_posts
from video_creation.voices import save_text_to_mp3

# استيراد الوحدات الجديدة
from automation.error_handler import handle_errors, critical_section, error_handler
from automation.gemini_content_generator import generate_video_content
from automation.youtube_uploader import upload_to_youtube
from automation.telegram_bot import send_notification, send_video_success, send_error
from automation.dashboard import log_video_created, log_video_published, log_error
from automation.smart_monitor import smart_monitor
from automation.message_router import send_telegram_message, send_telegram_error, send_telegram_success, send_telegram_progress

# إعداد نظام السجلات المحسن للعمل مع Render
def setup_logging():
    """إعداد نظام السجلات المتوافق مع Render"""
    # إنشاء مجلد logs إذا لم يكن موجوداً
    os.makedirs('logs', exist_ok=True)

    # إعداد التنسيق
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # إعداد معالجات السجلات
    handlers = [
        logging.StreamHandler(sys.stdout),  # للعرض في وحدة التحكم
        logging.StreamHandler(sys.stderr)   # للأخطاء
    ]

    # إضافة معالج الملف إذا كان ممكناً
    try:
        file_handler = logging.FileHandler('logs/main.log', encoding='utf-8')
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)
    except Exception as e:
        print(f"تحذير: لا يمكن إنشاء ملف السجل: {e}")

    # تطبيق الإعدادات
    for handler in handlers:
        handler.setFormatter(formatter)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers,
        force=True  # لإعادة تعيين الإعدادات الموجودة
    )

# تطبيق إعدادات السجلات
setup_logging()
logger = logging.getLogger(__name__)

__VERSION__ = "3.3.0"

print(
    """
██████╗ ███████╗██████╗ ██████╗ ██╗████████╗    ██╗   ██╗██╗██████╗ ███████╗ ██████╗     ███╗   ███╗ █████╗ ██╗  ██╗███████╗██████╗
██╔══██╗██╔════╝██╔══██╗██╔══██╗██║╚══██╔══╝    ██║   ██║██║██╔══██╗██╔════╝██╔═══██╗    ████╗ ████║██╔══██╗██║ ██╔╝██╔════╝██╔══██╗
██████╔╝█████╗  ██║  ██║██║  ██║██║   ██║       ██║   ██║██║██║  ██║█████╗  ██║   ██║    ██╔████╔██║███████║█████╔╝ █████╗  ██████╔╝
██╔══██╗██╔══╝  ██║  ██║██║  ██║██║   ██║       ╚██╗ ██╔╝██║██║  ██║██╔══╝  ██║   ██║    ██║╚██╔╝██║██╔══██║██╔═██╗ ██╔══╝  ██╔══██╗
██║  ██║███████╗██████╔╝██████╔╝██║   ██║        ╚████╔╝ ██║██████╔╝███████╗╚██████╔╝    ██║ ╚═╝ ██║██║  ██║██║  ██╗███████╗██║  ██║
╚═╝  ╚═╝╚══════╝╚═════╝ ╚═════╝ ╚═╝   ╚═╝         ╚═══╝  ╚═╝╚═════╝ ╚══════╝ ╚═════╝     ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝
"""
)
print_markdown(
    "### Thanks for using this tool! Feel free to contribute to this project on GitHub! If you have any questions, feel free to join my Discord server or submit a GitHub issue. You can find solutions to many common problems in the documentation: https://reddit-video-maker-bot.netlify.app/"
)
# checkversion(__VERSION__)  # معطل مؤقتاً لتجنب مشاكل الاتصال


@critical_section(context={'function': 'main_video_creation'})
def main(POST_ID=None) -> str:
    """
    الدالة الرئيسية لإنشاء الفيديو مع التكامل الكامل

    Returns:
        مسار ملف الفيديو المنشأ أو None في حالة الفشل
    """
    global redditid, reddit_object

    try:
        send_telegram_progress("🎬 بدء عملية إنشاء فيديو جديد", "تهيئة النظام", 6, 1)

        # جلب محتوى Reddit
        send_telegram_progress("📝 جلب محتوى Reddit", "الاتصال بـ Reddit API", 6, 2)
        reddit_object = get_subreddit_threads(POST_ID)
        redditid = id(reddit_object)

        send_telegram_success(f"تم جلب المحتوى بنجاح", f"العنوان: {reddit_object['thread_title'][:50]}...")

        # إنشاء محتوى الفيديو باستخدام Gemini AI
        send_telegram_progress("🤖 إنشاء المحتوى بواسطة Gemini AI", "معالجة النص", 6, 3)
        video_content = generate_video_content(
            reddit_object['thread_title'],
            str(reddit_object.get('thread_post', '')) + ' '.join([
                comment.get('comment_body', '') for comment in reddit_object.get('comments', [])
            ])[:2000],  # تحديد طول المحتوى
            reddit_object.get('subreddit', 'AskReddit')
        )

        send_telegram_success(f"تم إنشاء المحتوى بواسطة Gemini", f"العنوان الجديد: {video_content['title'][:50]}...")

        # حفظ معلومات المحتوى في reddit_object للاستخدام لاحقاً
        reddit_object['ai_title'] = video_content['title']
        reddit_object['ai_description'] = video_content['description']
        reddit_object['ai_hashtags'] = video_content['hashtags']

        # إنشاء الصوت مع معالجة ذكية للأخطاء
        try:
            send_telegram_progress("🎤 إنشاء الملفات الصوتية", "تحويل النص إلى صوت", 6, 4)
            length, number_of_comments = save_text_to_mp3(reddit_object)
            length = math.ceil(length)

            # التحقق من صحة طول الفيديو
            if length <= 0:
                logger.warning(f"⚠️ طول الفيديو غير صحيح: {length} ثانية")
                # محاولة حساب الطول من الملفات الصوتية المنشأة
                try:
                    import os
                    from moviepy.editor import AudioFileClip
                    reddit_id = reddit_object.get("thread_id", "unknown")
                    audio_path = f"assets/temp/{reddit_id}/mp3/title.mp3"
                    if os.path.exists(audio_path):
                        clip = AudioFileClip(audio_path)
                        length = max(10, math.ceil(clip.duration))  # على الأقل 10 ثوان
                        clip.close()
                        logger.info(f"✅ تم حساب الطول من الملف الصوتي: {length} ثانية")
                    else:
                        length = 30  # قيمة افتراضية
                        logger.warning(f"⚠️ استخدام طول افتراضي: {length} ثانية")
                except Exception as calc_error:
                    logger.warning(f"⚠️ فشل في حساب الطول: {calc_error}")
                    length = 30  # قيمة افتراضية

            smart_monitor.update_system_health("tts_generation", True, "تم إنشاء الصوت بنجاح")
            send_telegram_success("تم إنشاء الملفات الصوتية", f"المدة: {length} ثانية، التعليقات: {number_of_comments}")
        except Exception as e:
            smart_monitor.log_error("tts_engine_failure", str(e), {"reddit_id": reddit_object.get("thread_id")})
            send_telegram_error("فشل في إنشاء الملفات الصوتية", str(e))
            raise

        # التقاط لقطات الشاشة مع معالجة ذكية للأخطاء
        try:
            send_telegram_progress("📸 التقاط لقطات الشاشة", "تصوير المحتوى", 6, 5)
            get_screenshots_of_reddit_posts(reddit_object, number_of_comments)
            smart_monitor.update_system_health("screenshot_system", True, "تم التقاط الصور بنجاح")
            send_telegram_success("تم التقاط لقطات الشاشة", f"عدد الصور: {number_of_comments + 1}")
        except Exception as e:
            smart_monitor.log_error("screenshot_timeout", str(e), {"reddit_id": reddit_object.get("thread_id")})
            send_telegram_error("فشل في التقاط لقطات الشاشة", str(e))
            raise

        # إعداد الخلفية
        send_telegram_progress("🎬 إعداد خلفية الفيديو", "تحضير الملفات", 6, 6)
        bg_config = {
            "video": get_background_config("video"),
            "audio": get_background_config("audio"),
        }
        download_background_video(bg_config["video"])
        download_background_audio(bg_config["audio"])
        chop_background(bg_config, length, reddit_object)

        # إنشاء الفيديو النهائي
        send_telegram_progress("🎞️ إنشاء الفيديو النهائي", "دمج جميع العناصر", 6, 6)
        video_path = make_final_video(number_of_comments, length, reddit_object, bg_config)

        # تسجيل إنشاء الفيديو
        video_info = {
            'title': video_content['title'],
            'reddit_title': reddit_object['thread_title'],
            'subreddit': reddit_object.get('subreddit', 'AskReddit'),
            'length': length,
            'comments_count': number_of_comments
        }
        log_video_created(video_info)

        send_telegram_success("تم إنشاء الفيديو بنجاح!", f"المسار: {video_path}\nالمدة: {length} ثانية")

        # رفع الفيديو على YouTube
        if video_path and Path(video_path).exists():
            send_telegram_progress("📤 رفع الفيديو على YouTube", "الاتصال بـ YouTube API")

            video_url = upload_to_youtube(
                video_path,
                video_content['title'],
                video_content['description'],
                video_content['hashtags']
            )

            if video_url:
                send_telegram_success("🎉 تم نشر الفيديو بنجاح!", f"الرابط: {video_url}")

                # تسجيل النشر
                log_video_published(video_url)

                # إرسال إشعار نجاح مفصل
                send_video_success(video_path, video_url, video_content['title'], video_content['description'])

                return video_path
            else:
                send_telegram_error("فشل في رفع الفيديو على YouTube", f"الملف: {video_path}")
                send_error("فشل في رفع الفيديو على YouTube", f"الملف: {video_path}")
        else:
            send_telegram_error("لم يتم إنشاء ملف الفيديو", "فشل في عملية إنشاء الفيديو")
            send_error("لم يتم إنشاء ملف الفيديو", "فشل في عملية إنشاء الفيديو")

        return video_path

    except Exception as e:
        send_telegram_error("خطأ حرج في إنشاء الفيديو", str(e), {
            'function': 'main',
            'post_id': POST_ID,
            'reddit_object': reddit_object.get('thread_title', 'غير متوفر') if 'reddit_object' in locals() else 'غير متوفر'
        })

        # تسجيل الخطأ
        error_info = {
            'function': 'main',
            'post_id': POST_ID,
            'reddit_object': reddit_object.get('thread_title', 'غير متوفر') if 'reddit_object' in locals() else 'غير متوفر'
        }
        log_error(error_info)

        # إرسال إشعار خطأ
        send_error(f"خطأ في إنشاء الفيديو: {str(e)}", str(e))

        raise


def run_many(times) -> None:
    for x in range(1, times + 1):
        print_step(
            f'on the {x}{("th", "st", "nd", "rd", "th", "th", "th", "th", "th", "th")[x % 10]} iteration of {times}'
        )  # correct 1st 2nd 3rd 4th 5th....
        main()
        Popen("cls" if name == "nt" else "clear", shell=True).wait()


def shutdown() -> NoReturn:
    if "redditid" in globals():
        print_markdown("## Clearing temp files")
        cleanup(redditid)

    print("Exiting...")
    sys.exit()


if __name__ == "__main__":
    if sys.version_info.major != 3 or sys.version_info.minor < 10:
        print(
            "Hey! This program requires Python 3.10 or higher. Please install Python 3.10+ and try again."
        )
        sys.exit()
    ffmpeg_install()
    directory = Path().absolute()
    config = settings.check_toml(
        f"{directory}/utils/.config.template.toml", f"{directory}/config.toml"
    )
    config is False and sys.exit()

    # فحص الإعدادات مع النظام الذكي
    print_step("Checking configuration with smart system...")
    config_valid = check_config_with_telegram_notifications()

    if not config_valid:
        print_substep(
            "⚠️ Configuration issues detected! Check Telegram for details and solutions.",
            "bold yellow",
        )
        print_substep(
            "🤖 Smart system will attempt auto-fix. Check Telegram bot for updates.",
            "bold blue",
        )
        # لا نوقف البرنامج، بل نتركه يحاول العمل مع الإصلاحات التلقائية
    try:
        if config["reddit"]["thread"]["post_id"]:
            for index, post_id in enumerate(config["reddit"]["thread"]["post_id"].split("+")):
                index += 1
                print_step(
                    f'on the {index}{("st" if index % 10 == 1 else ("nd" if index % 10 == 2 else ("rd" if index % 10 == 3 else "th")))} post of {len(config["reddit"]["thread"]["post_id"].split("+"))}'
                )
                main(post_id)
                Popen("cls" if name == "nt" else "clear", shell=True).wait()
        elif config["settings"]["times_to_run"]:
            run_many(config["settings"]["times_to_run"])
        else:
            main()
    except KeyboardInterrupt:
        shutdown()
    except ResponseException:
        print_markdown("## Invalid credentials")
        print_markdown("Please check your credentials in the config.toml file")
        shutdown()
    except Exception as err:
        # تسجيل الخطأ في النظام الذكي
        error_context = {
            "version": __VERSION__,
            "config": config["settings"] if config else {},
            "error_type": type(err).__name__
        }
        smart_monitor.log_error("main_execution_error", str(err), error_context)

        config["settings"]["tts"]["tiktok_sessionid"] = "REDACTED"
        config["settings"]["tts"]["elevenlabs_api_key"] = "REDACTED"
        print_step(
            f"Sorry, something went wrong with this version! Try again, and feel free to report this issue at GitHub or the Discord community.\n"
            f"Version: {__VERSION__} \n"
            f"Error: {err} \n"
            f'Config: {config["settings"]}'
        )
        raise err
