#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل محسن للبوت في بيئة Render
يحل مشاكل الشبكة والاتصال تلقائياً
"""

import os
import sys
import logging
import asyncio
import signal
from pathlib import Path

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def setup_environment():
    """إعداد البيئة للتشغيل في Render"""
    logger.info("🚀 إعداد البيئة لـ Render...")
    
    # تعيين متغيرات البيئة المطلوبة
    os.environ.setdefault('RENDER', 'true')
    os.environ.setdefault('PYTHONUNBUFFERED', '1')
    os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
    
    # إنشاء المجلدات المطلوبة
    required_dirs = [
        'logs',
        'temp',
        'cache',
        'security/logs',
        'security/quarantine',
        'security_logs'
    ]
    
    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ مجلد: {dir_path}")
    
    # إنشاء الملفات المطلوبة
    required_files = [
        'user_channels.json',
        'all_users.json',
        'user_feedback.json',
        'user_mods_status.json',
        'user_blocked_mods.json',
        'user_invitations.json',
        'user_subscriptions.json',
        'user_feature_activation.json',
        'admin_settings.json',
        'admin_processed_mods.json',
        'pending_publication.json'
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('{}')
            logger.info(f"✅ ملف: {file_path}")

def apply_network_fixes():
    """تطبيق إصلاحات الشبكة لبيئة Render"""
    logger.info("🔧 تطبيق إصلاحات الشبكة...")
    
    try:
        # تشغيل أداة إصلاح الشبكة
        from render_network_fix import RenderNetworkFixer
        
        fixer = RenderNetworkFixer()
        results = fixer.run_full_diagnosis()
        
        if results['basic_connectivity']:
            logger.info("✅ إصلاحات الشبكة تمت بنجاح")
            return True
        else:
            logger.warning("⚠️ بعض مشاكل الشبكة لم تحل، لكن سيتم المتابعة")
            return True  # نتابع حتى لو كانت هناك مشاكل
            
    except Exception as e:
        logger.warning(f"⚠️ فشل في تطبيق إصلاحات الشبكة: {e}")
        return True  # نتابع حتى لو فشلت الإصلاحات

def check_required_env_vars():
    """فحص متغيرات البيئة المطلوبة مع إصلاح تلقائي"""
    logger.info("🔍 فحص متغيرات البيئة...")

    # إصلاح تلقائي لمتغيرات البيئة - تعيين القيم مباشرة
    env_fixes = {
        'TELEGRAM_BOT_TOKEN': '**********************************************',
        'BOT_TOKEN': '**********************************************',
        'SUPABASE_URL': 'https://ytqxxodyecdeosnqoure.supabase.co',
        'SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
        'ADMIN_CHAT_ID': '7513880877',
        'RENDER': 'true',
        'RENDER_SERVICE_TYPE': 'web',
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8'
    }

    fixes_applied = []
    for var, value in env_fixes.items():
        if not os.getenv(var):
            os.environ[var] = value
            fixes_applied.append(var)
            logger.info(f"✅ تم تعيين {var}")

    if fixes_applied:
        logger.info(f"🔧 تم إصلاح {len(fixes_applied)} متغير بيئة")

    # التحقق النهائي
    required_vars = ['TELEGRAM_BOT_TOKEN', 'SUPABASE_URL', 'SUPABASE_KEY']
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            # إخفاء جزء من القيمة للأمان
            value = os.getenv(var)
            masked_value = value[:10] + "..." if len(value) > 10 else value
            logger.info(f"✅ {var}: {masked_value}")

    if missing_vars:
        logger.error(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        return False

    logger.info("✅ جميع متغيرات البيئة موجودة")
    return True

def setup_signal_handlers():
    """إعداد معالجات الإشارات للإغلاق الآمن"""
    def signal_handler(signum, frame):
        logger.info(f"📡 تم استلام إشارة {signum}، إغلاق آمن...")
        sys.exit(0)
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

async def start_bot():
    """تشغيل البوت الرئيسي"""
    logger.info("🤖 بدء تشغيل البوت...")
    
    try:
        # استيراد وتشغيل البوت الرئيسي
        import main
        
        # تشغيل البوت
        await main.main()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        raise

def run_web_server():
    """تشغيل خادم الويب للحفاظ على الخدمة نشطة"""
    try:
        from web_server import run_web_server as start_web_server
        logger.info("🌐 تشغيل خادم الويب...")
        start_web_server()
    except ImportError:
        logger.warning("⚠️ خادم الويب غير متوفر")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل خادم الويب: {e}")

async def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء تشغيل البوت في بيئة Render")
    logger.info("=" * 50)
    
    try:
        # إعداد معالجات الإشارات
        setup_signal_handlers()
        
        # إعداد البيئة
        setup_environment()
        
        # تطبيق إصلاحات الشبكة
        network_ok = apply_network_fixes()
        if not network_ok:
            logger.warning("⚠️ مشاكل في الشبكة، لكن سيتم المتابعة")
        
        # فحص متغيرات البيئة
        env_ok = check_required_env_vars()
        if not env_ok:
            logger.error("❌ متغيرات البيئة مفقودة، لا يمكن تشغيل البوت")
            sys.exit(1)
        
        # تحديد نوع الخدمة
        service_type = os.getenv('RENDER_SERVICE_TYPE', 'worker')
        logger.info(f"📋 نوع الخدمة: {service_type}")
        
        if service_type == 'web':
            # إذا كانت خدمة ويب، نشغل خادم الويب مع البوت
            logger.info("🌐 تشغيل كخدمة ويب...")
            
            # تشغيل البوت في مهمة منفصلة
            bot_task = asyncio.create_task(start_bot())
            
            # تشغيل خادم الويب في خيط منفصل
            import threading
            web_thread = threading.Thread(target=run_web_server, daemon=True)
            web_thread.start()
            
            # انتظار البوت
            await bot_task
            
        else:
            # إذا كانت خدمة عامل، نشغل البوت فقط
            logger.info("⚙️ تشغيل كخدمة عامل...")
            await start_bot()
            
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # تشغيل البوت
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت")
    except Exception as e:
        logger.error(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)
